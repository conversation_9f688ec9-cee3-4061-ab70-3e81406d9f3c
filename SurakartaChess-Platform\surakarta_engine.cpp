#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// 棋子类型定义
#define BLACK 0
#define WHITE 1
#define EMPTY 2

// 棋盘大小
#define BOARD_SIZE 6

// 点结构
typedef struct _Point {
    int x, y;
} Point;

// 步结构
typedef struct _Step {
    Point start, end;
    int value;
} Step;

// 全局变量
int Board[BOARD_SIZE][BOARD_SIZE];  // 棋盘结构
int computerSide;                   // 己方执棋颜色
int gameStarted = 0;               // 对局开始标记

// 函数声明
void initializeBoard();
void printBoard();
int isValidMove(Point start, Point end);
int canCapture(Point start, Point end);
int makeMove(Point start, Point end);
Step generateBestMove();
int evaluatePosition();
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]);
int countPieces(int color);
int isGameOver();

// 初始化棋盘
void initializeBoard() {
    int i, j;

    // 初始化棋盘为空
    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }

    // 设置初始棋子位置
    // 黑子在上方两行
    for (i = 0; i < 2; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = BLACK;
        }
    }

    // 白子在下方两行
    for (i = 4; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = WHITE;
        }
    }
}

// 打印棋盘（调试用）
void printBoard() {
    int i, j;
    printf("  A B C D E F\n");
    for (i = 0; i < BOARD_SIZE; i++) {
        printf("%d ", i + 1);
        for (j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == BLACK) {
                printf("B ");
            } else if (Board[i][j] == WHITE) {
                printf("W ");
            } else {
                printf(". ");
            }
        }
        printf("\n");
    }
    printf("\n");
}

// 检查是否为有效的基本移动（8方向，一格）
int isValidMove(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否为空
    if (Board[end.x][end.y] != EMPTY) {
        return 0;
    }

    // 检查是否为8方向移动一格
    int dx = abs(end.x - start.x);
    int dy = abs(end.y - start.y);

    if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
        return 1;
    }

    return 0;
}

// 弧线吃子判断函数声明
int isValidArcCapture(int startX, int startY, int endX, int endY);

// 吃子判断（使用弧线路径检查）
int canCapture(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否有对方棋子
    if (Board[end.x][end.y] != (computerSide ^ 1)) {
        return 0;
    }

    // 使用弧线路径检查
    return isValidArcCapture(start.x, start.y, end.x, end.y);
}

// 执行移动
int makeMove(Point start, Point end) {
    if (isValidMove(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    } else if (canCapture(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    }
    return 0;
}

// 简单的位置评估函数
int evaluatePosition() {
    int myPieces = countPieces(computerSide);
    int opponentPieces = countPieces(computerSide ^ 1);

    // 简单评估：己方棋子数 - 对方棋子数
    return myPieces - opponentPieces;
}

// 计算指定颜色的棋子数量
int countPieces(int color) {
    int count = 0;
    int i, j;

    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == color) {
                count++;
            }
        }
    }

    return count;
}

// 检查游戏是否结束
int isGameOver() {
    int blackCount = countPieces(BLACK);
    int whiteCount = countPieces(WHITE);

    // 如果任一方棋子全部被吃掉
    if (blackCount == 0 || whiteCount == 0) {
        return 1;
    }

    // TODO: 检查是否还有可能的移动
    return 0;
}

// 复制棋盘
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]) {
    int i, j;
    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            dest[i][j] = src[i][j];
        }
    }
}

// 生成最佳移动（包含吃子逻辑）
Step generateBestMove() {
    Step bestMove;
    bestMove.start.x = -1;
    bestMove.start.y = -1;
    bestMove.end.x = -1;
    bestMove.end.y = -1;
    bestMove.value = -1000;

    int i, j, ti, tj;

    // 遍历所有己方棋子
    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                Point start = {i, j};

                // 首先尝试吃子移动（优先级更高）
                for (ti = 0; ti < BOARD_SIZE; ti++) {
                    for (tj = 0; tj < BOARD_SIZE; tj++) {
                        Point end = {ti, tj};

                        if (canCapture(start, end)) {
                            // 尝试吃子并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition() + 10; // 吃子奖励

                            if (value > bestMove.value) {
                                bestMove.start = start;
                                bestMove.end = end;
                                bestMove.value = value;
                            }

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }

                // 然后尝试普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;

                        Point end = {i + di, j + dj};

                        if (isValidMove(start, end)) {
                            // 尝试移动并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition();

                            if (value > bestMove.value) {
                                bestMove.start = start;
                                bestMove.end = end;
                                bestMove.value = value;
                            }

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }
            }
        }
    }

    return bestMove;
}

// 主函数 - 实现SAU平台通信协议
int main() {
    Step step;
    char message[256];

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 程序主循环
    while (1) {
        fflush(stdout);

        // 获取平台消息
        scanf("%s", message);

        // 分析命令
        if (strcmp(message, "move") == 0) {
            // 行棋命令
            scanf("%s", message);
            fflush(stdin);

            // 解析对手着法
            step.start.x = message[0] - 'A';
            step.start.y = message[1] - 'A';
            step.end.x = message[2] - 'A';
            step.end.y = message[3] - 'A';

            // 处理对手行棋
            Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
            Board[step.start.x][step.start.y] = EMPTY;

            // 生成己方着法
            step = generateBestMove();

            // 处理己方行棋
            if (step.start.x != -1) {
                Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
                Board[step.start.x][step.start.y] = EMPTY;

                // 输出着法
                printf("move %c%c%c%c\n",
                       step.start.x + 'A', step.start.y + 'A',
                       step.end.x + 'A', step.end.y + 'A');
            }
        }
        else if (strcmp(message, "new") == 0) {
            // 建立新棋局
            scanf("%s", message);
            fflush(stdin);

            if (strcmp(message, "black") == 0) {
                computerSide = BLACK;
            } else {
                computerSide = WHITE;
            }

            // 初始化棋局
            initializeBoard();
            gameStarted = 1;

            if (computerSide == BLACK) {
                // 黑方先手，生成第一手着法
                step = generateBestMove();

                if (step.start.x != -1) {
                    // 处理己方行棋
                    Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
                    Board[step.start.x][step.start.y] = EMPTY;

                    // 输出着法
                    printf("move %c%c%c%c\n",
                           step.start.x + 'A', step.start.y + 'A',
                           step.end.x + 'A', step.end.y + 'A');
                }
            }
        }
        else if (strcmp(message, "error") == 0) {
            // 着法错误
            fflush(stdin);
            // 重新生成着法
            step = generateBestMove();
            if (step.start.x != -1) {
                printf("move %c%c%c%c\n",
                       step.start.x + 'A', step.start.y + 'A',
                       step.end.x + 'A', step.end.y + 'A');
            }
        }
        else if (strcmp(message, "name?") == 0) {
            // 询问引擎名
            fflush(stdin);
            printf("name SurakartaEngine\n");
        }
        else if (strcmp(message, "end") == 0) {
            // 对局结束
            fflush(stdin);
            gameStarted = 0;
        }
        else if (strcmp(message, "quit") == 0) {
            // 退出引擎
            fflush(stdin);
            printf("Quit!\n");
            break;
        }
    }

    return 0;
}