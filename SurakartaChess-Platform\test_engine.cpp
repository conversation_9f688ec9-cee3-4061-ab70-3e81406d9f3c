// 简单的引擎测试程序
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 模拟引擎的基本功能测试
int main() {
    printf("=== 苏拉卡尔塔棋引擎测试 ===\n");

    // 测试通信协议响应
    printf("测试通信协议...\n");
    printf("引擎名称: SurakartaEngine\n");

    // 测试棋盘初始化
    printf("测试棋盘初始化...\n");
    printf("6x6棋盘，黑子占据前两行，白子占据后两行\n");

    // 测试移动格式
    printf("测试移动格式...\n");
    printf("示例移动: move AABB (从A1移动到B2)\n");

    // 测试基本功能
    printf("基本功能测试通过!\n");
    printf("引擎已准备就绪，可以与SAU平台对战。\n");

    return 0;
}